{"name": "@anwb/sombrero-netlify", "version": "1.18.2", "title": "Bricks Application Frontend Platform", "team": "Bricks", "description": "ANWB.nl frontend application platform and Bloomreach integration layer", "license": "ISC", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint --cache .", "typecheck": "echo \"Not yet implemented\"", "test": "echo \"Not yet implemented\"", "test:ui": "echo \"Not yet implemented\"", "test:watch": "echo \"Not yet implemented\"", "test:coverage": "echo \"Not yet implemented\""}, "repository": {"type": "git", "url": "https://gitlab.anwbonline.nl/anwb/common/frontend/sombrero"}, "browserslist": ["> 1% in NL"], "dependencies": {"@anwb/account-applications": "workspace:*", "@anwb/ciam-authentication-status-provider": "workspace:*", "@anwb/customer-recognition-application": "0.0.21", "@anwb/eropuit-search-application": "1.5.1", "@anwb/insurance-sales-applications": "workspace:*", "@anwb/insurance-service-applications": "workspace:*", "@anwb/iris-applications": "workspace:*", "@anwb/iris-search-component": "8.7.0", "@anwb/omnia-iris-widget": "6.0.2", "@anwb/personalised-applications": "workspace:*", "@anwb/platform-applications": "workspace:*", "@anwb/platform-bloomreach": "workspace:*", "@anwb/platform-netlify-plugin-smoke-test": "workspace:*", "@anwb/platform-netlify-plugin-speed-curve": "workspace:*", "@anwb/platform-support": "workspace:*", "@anwb/platform-third-parties": "workspace:*", "@anwb/poncho": "4.64.3", "@anwb/service-and-contact-component": "5.8.3", "@anwb/traffic-applications": "workspace:*", "@anwb/travel-applications": "workspace:*", "@anwb/travel-b2b": "workspace:*", "@anwb/travel-bookingdialog": "0.0.201", "@anwb/travel-productpage": "0.0.223", "@anwb/travel-search-application": "0.0.41", "@anwb/travel-search-widget": "0.0.147", "@anwb/webshop-applications": "workspace:*", "@anwb/webshop-checkout-wizard-page-next": "workspace:*", "@anwb/webshop-content-page-next": "workspace:*", "@anwb/webshop-home-page-next": "workspace:*", "@anwb/webshop-popular-products-next": "workspace:*", "@anwb/webshop-product-detail-page-next": "workspace:*", "@anwb/webshop-product-listing-page-next": "workspace:*", "@anwb/webshop-search-result-page-next": "workspace:*", "@anwb/webshop-seller-page-next": "workspace:*", "@anwb/webshop-shopping-cart-page-next": "workspace:*", "@anwb/webshop-store-detail-page-next": "workspace:*", "@anwb/webshop-stores-page-next": "workspace:*", "@anwb/webshop-thank-you-page-next": "workspace:*", "@anwb/widget-travel-panel": "0.11.71", "@bloomreach/react-sdk": "^24.1.1", "@bloomreach/spa-sdk": "^24.1.1", "@epic-web/cachified": "^5.5.2", "@mjackson/headers": "^0.10.0", "@netlify/blobs": "^8.2.0", "@netlify/edge-functions": "^2.12.0", "@netlify/functions": "^3.1.8", "@netlify/plugin-nextjs": "4.41.3", "@optimizely/optimizely-sdk": "^5.3.5", "@sentry/nextjs": "^7.120.3", "@tanstack/react-query": "^4.40.0", "axios": "^1.9.0", "axios-https-proxy-fix": "^0.17.1", "cookies-next": "^4.3.0", "effect": "^3.15.1", "fastify": "^5.3.3", "follow-redirects": "^1.15.8", "hono": "^4.7.9", "ipaddr.js": "^2.2.0", "jsonp-es6": "^1.0.0", "next": "^12.3.7", "ramda": "^0.30.1", "react": "^17.0.2", "react-dom": "^17.0.2", "react-use": "^17.6.0", "smartbanner.js": "^1.25.0", "styled-components": "^5.3.11", "webpack-modules": "^1.0.0", "xml-js": "^1.6.11", "zod": "^3.25.0"}, "devDependencies": {"@anwb/tools-eslint-config": "workspace:*", "@next/bundle-analyzer": "^12.3.7", "@next/env": "^12.3.7", "@total-typescript/tsconfig": "^1.0.4", "@types/node": "^22.15.17", "@types/ramda": "^0.30.2", "@types/react": "^17.0.83", "@types/react-dom": "^17.0.26", "@types/styled-components": "^5.1.34", "@vitest/coverage-istanbul": "^3.0.6", "@vitest/ui": "^3.0.6", "next-compose-plugins": "^2.2.1", "next-images": "^1.8.5", "next-transpile-modules": "^9.1.0", "typescript": "^5.8.3", "vitest": "^3.0.6", "webpack": "^5.99.8"}, "engines": {"node": ">=22.16.0"}}