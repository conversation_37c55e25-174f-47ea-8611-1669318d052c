import { UserAuthSchema } from "../schemas/user-auth-schema";

/**
 * Higher-order function that wraps API calls with error handling
 *
 * This wrapper catches any errors thrown by the wrapped function and returns
 * undefined instead, providing a graceful fallback for authentication
 * failures.
 *
 * @param callback - The function to wrap with error handling
 * @returns A wrapped function that returns undefined on errors instead of
 *   throwing
 */
const withErrorHandler =
	<TFunc extends (...args: Array<any>) => any>(callback: TFunc) =>
	async (
		...params: Parameters<TFunc>
	): Promise<ReturnType<TFunc> | undefined> => {
		try {
			return await callback(...params);
		} catch (error) {
			// Gracefully handle authentication errors by returning undefined
			return undefined;
		}
	};

/**
 * Makes an authenticated API call to validate a user's access token
 *
 * This function calls the APIGEE token validation endpoint to verify that the
 * user's access token is still valid and retrieve token metadata.
 *
 * @param accessToken - The user's access token to validate
 * @returns Promise that resolves to validated user authentication data
 * @throws Error if the API request fails or token is invalid
 */
const apiCall = async (accessToken: string) => {
	const url = process.env.NEXT_PUBLIC_APIGEE_URL ?? "";
	const clientId = process.env.APIGEE_NETLIFY_KEY ?? "";

	const response = await fetch(`${url}/v1/auth/tokeninfo`, {
		headers: {
			Authorization: `Bearer ${accessToken}`,
			"Content-Type": "application/json",
			"x-anwb-netlify-client-id": clientId,
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch user auth state");
	}

	// Parse and validate the response against the expected schema
	return UserAuthSchema.parse(await response.json());
};

/**
 * Fetches and validates user authentication state with error handling
 *
 * This function validates a user's access token by calling the authentication
 * service. It includes automatic error handling and will return undefined if
 * the token is invalid or the service is unavailable.
 *
 * @param accessToken - The user's access token to validate
 * @returns Promise that resolves to user auth data or undefined if validation
 *   fails
 */
export const fetchUserAuthState = withErrorHandler(apiCall);
