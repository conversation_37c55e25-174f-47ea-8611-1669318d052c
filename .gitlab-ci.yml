# ======================
# CI Stages
# ======================

stages:
  - prepare
  - test
  - renovate
  - deploy

# ======================
# External includes
# ======================

include:
  - template: Jobs/SAST.latest.gitlab-ci.yml
  - template: Jobs/Secret-Detection.latest.gitlab-ci.yml
  - template: Jobs/Dependency-Scanning.latest.gitlab-ci.yml

# ======================
# Global Snippets
# ======================

.sbr-npm-cache: &sbr-npm-cache
  key:
    files:
      - pnpm-lock.yaml
  paths:
    - .pnpm-store
  policy: pull

.sbr-prepare-ci: &sbr-prepare-ci
  - npm uninstall -g yarn pnpm
  - npm install --global corepack@0.31.0
  - npm install --global turbo@2.4.0
  - corepack prepare --activate
  - pnpm config set store-dir .pnpm-store

# ======================
# Default stage
# ======================

default:
  image: node:22.16.0

# ======================
# Prepare stage
# ======================

prepare:install-dependencies:
  stage: prepare
  cache:
    - <<: *sbr-npm-cache
      policy: pull-push
  before_script:
    - *sbr-prepare-ci
  script:
    - pnpm install --frozen-lockfile
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
    - if: '$CI_PIPELINE_SOURCE == "schedule"'

# ======================
# Test stage
# ======================

.sbr-tester:
  stage: test
  cache:
    - <<: *sbr-npm-cache
  before_script:
    - *sbr-prepare-ci
    - pnpm install --frozen-lockfile --prefer-offline
  needs:
    - job: prepare:install-dependencies
      artifacts: false
  tags:
    - ondemand-xl
  interruptible: true
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'

test:format:
  extends: .sbr-tester
  script:
    - pnpm run format

test:lint:
  extends: .sbr-tester
  script:
    - pnpm run lint --log-order=grouped -- --quiet

test:typecheck:
  extends: .sbr-tester
  script:
    - pnpm run typecheck --log-order=grouped

test:unit:
  extends: .sbr-tester
  script:
    - pnpm run test --log-order=grouped -- --coverage --coverage.reporter="json-summary"
    - node ./scripts/code-coverage.mjs
  coverage: '/total[^\x{2502}]*\x{2502}[^\x{2502}]*\s+([\d\.]+)/'

sast:
  stage: test

# ======================
# Deploy stage
# ======================

deploy:wait-for-netlify:
  stage: deploy
  cache:
    - <<: *sbr-npm-cache
  before_script:
    - *sbr-prepare-ci
    - pnpm install --frozen-lockfile --prefer-offline
  needs:
    - job: prepare:install-dependencies
      artifacts: false
  tags:
    - ondemand-xl
  interruptible: true
  script:
    - pnpm sbr cnd -b $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME -i $NETLIFY_SITE_ID -t $NETLIFY_AUTH_TOKEN
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "main"'

# ======================
# Renovate stage
# ======================

renovate:
  stage: renovate
  cache:
    - *sbr-npm-cache
  needs:
    - job: prepare:install-dependencies
      artifacts: false
  tags:
    - ondemand-xl
  interruptible: true
  before_script:
    - *sbr-prepare-ci
    - pnpm install --frozen-lockfile --prefer-offline
  script:
    - pnpm run renovate
  rules:
    - if: '$CI_PIPELINE_SOURCE == "schedule"'
      when: always
