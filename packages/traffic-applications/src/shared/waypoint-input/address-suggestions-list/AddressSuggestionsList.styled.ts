import type { DefaultThemeProps } from "@anwb/poncho/design-tokens/theme";
import type { Theme } from "@anwb/poncho/design-tokens/theme/types/theme";

import PonchoIcon from "@anwb/poncho/components/icon";
import Typography from "@anwb/poncho/components/typography";
import { pxToRem } from "@anwb/poncho/design-tokens/style-utilities";
import styled, { css } from "styled-components";

export const Group = styled.ul`
	padding-left: 0;
	margin: 0;
`;

const Text = (theme: Theme) => css`
	padding: 0 ${pxToRem(theme.spacing["400"])};
	font-size: ${pxToRem(theme.fontSize["300"])};
	line-height: 1.4em;
`;

export const Error = styled(Typography)(
	({ theme }: DefaultThemeProps) => css`
		${Text(theme)}
		color: ${theme.colors.error.textSupport};
	`,
);

export const Hint = styled(Typography)(
	({ theme }: DefaultThemeProps) => css`
		${Text(theme)}
		margin-top: 0;
		margin-bottom: 0;
	`,
);

export const Suggestion = styled(Typography)(
	({
		$isHighlighted,
		theme,
	}: DefaultThemeProps & { $isHighlighted: boolean }) => css`
		display: flex;
		gap: ${pxToRem(theme.spacing["200"])};
		font-size: ${pxToRem(theme.fontSize["400"])};
		margin: 0;
		padding: 0 ${pxToRem(theme.spacing["400"])};
		cursor: pointer;

		&:hover {
			color: ${theme.colors.highlight.textHover};
			background: ${theme.colors.extralight.fillIndicationBar};
		}

		// Add border to the inner div that does not span the full width but aligns with the content
		&:not(:last-child) > div {
			border-bottom: ${pxToRem(theme.borderWidth["1"])} solid
				${theme.colors.light.divider};
		}

		div {
			padding: ${pxToRem(theme.spacing["200"])} 0;
			width: 100%;
		}

		${$isHighlighted &&
		css`
			color: ${theme.colors.highlight.textHover};
			background: ${theme.colors.extralight.fillIndicationBar};
		`}
	`,
);

export const RecentTitle = styled(Typography)(
	({ theme }: DefaultThemeProps) => css`
		margin-top: 0;
		margin-bottom: ${pxToRem(theme.spacing["100"])};
		padding-left: ${pxToRem(theme.spacing["400"])};
		padding-right: ${pxToRem(theme.spacing["400"])};
		list-style-type: none;
	`,
);

/**
 * We need this additional dom element so we can have a list item that spans the
 * entire width of the list (required for highlighting). While the border only
 * spans the width of the content.
 */
export const Content = styled.div(
	({ theme }: DefaultThemeProps) => css`
		display: grid;
		grid-template-columns: auto 1fr;
		align-items: start;
		font-size: ${pxToRem(theme.fontSize["400"])};
		gap: 0 ${pxToRem(theme.spacing["200"])};
	`,
);

export const Icon = styled(PonchoIcon)(
	() => css`
		grid-row: span 2;
	`,
);

export const PoiAddress = styled.span(
	({ theme }: DefaultThemeProps) => css`
		color: ${theme.colors.base.textBodyInactive};
		font-size: ${pxToRem(theme.fontSize["300"])};
		margin: 0;
	`,
);
