import Icon, { type IconSize } from "@anwb/poncho/components/icon";
import Typography from "@anwb/poncho/components/typography";
import { VisuallyHidden } from "@anwb/poncho/components/visually-hidden";
import { useApplicationSize } from "@anwb/poncho/providers/providers-application";
import { formatDuration, intervalToDuration } from "date-fns";
import { nl } from "date-fns/locale";

import { routeIsCar } from "../../../../../api/routing/route-type-guards";
import {
	convertDateTime,
	convertDistance,
	convertSecondsToMinutes,
	convertTimeAriaLabel,
} from "../../../../../utils/utils";
import { useActiveRoute } from "../../../context/active-route-context/active-route.context";
import {
	useTravelNow,
	useTravelTime,
} from "../../../hooks/use-active-route.hook";
import * as Styled from "./TravelSummary.styled";

export function TravelSummary({ children }: { children?: React.ReactNode }) {
	const leavingToday = useTravelNow();

	return leavingToday ? (
		<Styled.Container>{children}</Styled.Container>
	) : (
		<Styled.ContainerLater>{children}</Styled.ContainerLater>
	);
}

function Summary() {
	const activeRoute = useActiveRoute();
	const leavingToday = useTravelNow();

	if (!activeRoute) {
		return null;
	}

	return leavingToday ? (
		<>
			<TravelTime />
			<Styled.VerticalDivider />
			<Arrival />
		</>
	) : (
		<>
			<TravelTime />
			<Styled.HorizontalDivider />
			<Departure />
			<Styled.VerticalDivider />
			<Arrival />
		</>
	);
}

function TravelTime() {
	const leavingToday = useTravelNow();
	const activeRoute = useActiveRoute();
	const travelTime = useTravelTime();

	if (!activeRoute || !travelTime) {
		return null;
	}

	const travelDistance = convertDistance(activeRoute.summary.distanceInMeters);

	const duration = intervalToDuration({
		end: activeRoute.summary.durationInSeconds * 1000,
		start: 0,
	});

	const timeAriaLabel = formatDuration(
		{
			hours: duration.hours,
			minutes: duration.minutes,
		},
		{ locale: nl },
	);

	const distanceAriaLabel = convertDistance(
		activeRoute.summary.distanceInMeters,
		true,
	);

	return (
		<Styled.TimeInfo>
			<VisuallyHidden>
				<Typography tagName="h3">Reistijd</Typography>
			</VisuallyHidden>
			<Styled.TimeTitle
				aria-label={timeAriaLabel}
				tagName="span"
				variant="content-subtitle"
			>
				<time aria-hidden>{travelTime}</time>
			</Styled.TimeTitle>

			<Styled.Distance
				$leavingToday={leavingToday}
				aria-label={distanceAriaLabel}
				tagName="span"
				variant="body-text"
			>
				<span aria-hidden>{travelDistance}</span>
			</Styled.Distance>
		</Styled.TimeInfo>
	);
}

function Departure() {
	const activeRoute = useActiveRoute();
	const leavingToday = useTravelNow();

	if (!activeRoute || leavingToday) {
		return null;
	}
	const dateTime = convertDateTime(activeRoute.summary.departure);

	return (
		<Styled.Departure>
			<Styled.Title tagName="h3" variant="label-title">
				Vertrek
			</Styled.Title>
			<Typography tagName="span" variant="body-text">
				<time dateTime={dateTime}>{dateTime}</time>
			</Typography>
		</Styled.Departure>
	);
}

function Arrival() {
	const activeRoute = useActiveRoute();

	if (!activeRoute?.summary.arrival) {
		return null;
	}

	const dateTime = convertDateTime(activeRoute.summary.arrival);

	return (
		<Styled.Arrival>
			<Styled.Title tagName="h3" variant="label-title">
				Aankomst
			</Styled.Title>

			<Typography
				aria-label={convertTimeAriaLabel(dateTime)}
				tagName="span"
				variant="body-text"
			>
				<time aria-hidden>{dateTime}</time>
			</Typography>
		</Styled.Arrival>
	);
}

function Delay() {
	const applicationSize = useApplicationSize();
	const activeRoute = useActiveRoute();
	const leavingNow = useTravelNow();

	if (!activeRoute || !leavingNow) {
		return null;
	}
	const delay = routeIsCar(activeRoute)
		? convertSecondsToMinutes(activeRoute.summary.delayInSeconds)
		: 0;
	const iconSize: IconSize = applicationSize === "small" ? "lg" : "xl";

	return (
		<Styled.DelayInfo $hasDelay={delay > 0} tagName="span">
			<Icon size={iconSize} type="ui" variant="traffic" />
			{delay > 0 ? `Incl. ${delay} min. vertraging` : "Geen vertraging"}
		</Styled.DelayInfo>
	);
}

TravelSummary.Summary = Summary;
TravelSummary.Delay = Delay;
