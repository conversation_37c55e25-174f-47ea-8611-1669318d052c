import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { addDays, addMinutes, format } from "date-fns";

import type { CarRoute } from "../../../../../api/routing/routing-api.types";

import { CAR_ROUTE_MOCK } from "../../../../../api/routing/routing-input-api.mock";
import { StoryContainer } from "../../../../../utils/utils-storybook";
import { ActiveRouteContext } from "../../../context/active-route-context/active-route.context";
import { TravelSummary } from "./TravelSummary";

const meta: Meta<typeof TravelSummary> = {
	component: TravelSummary,
	decorators: [
		(Story, { parameters }) => {
			const routeData = (parameters.data || CAR_ROUTE_MOCK) as CarRoute;

			return (
				<ActiveRouteContext.Provider value={routeData}>
					<StoryContainer width={400}>
						<Story {...parameters} />
					</StoryContainer>
				</ActiveRouteContext.Provider>
			);
		},
	],
	parameters: {
		layout: "centered",
	},
	title: "Traffic/Routing/Sidepanel/TravelSummary",
};

export default meta;
type Story = StoryObj<typeof meta>;

const CAR_MOCK_NOW = {
	...CAR_ROUTE_MOCK,
	summary: {
		...CAR_ROUTE_MOCK.summary,
		arrival: format(addMinutes(new Date(), 30), "yyyy-MM-dd'T'HH:mm:ss.SSS"),
		departure: format(new Date(), "yyyy-MM-dd'T'HH:mm:ss.SSS"),
	},
};

const CAR_MOCK_KILOMETERS = {
	...CAR_ROUTE_MOCK,
	summary: {
		...CAR_ROUTE_MOCK.summary,
		arrival: "2025-06-20T20:19:49Z",
		countryCodesAlongTheRoute: ["NLD"],
		departure: "2025-06-20T20:04:25Z",
		distanceInMeters: ********,
	},
};

export const Default: Story = {
	args: {
		children: (
			<>
				<TravelSummary.Summary />
				<TravelSummary.Delay />
			</>
		),
	},
	parameters: {
		data: CAR_MOCK_KILOMETERS,
	},
};

export const Now: Story = {
	args: {
		children: (
			<>
				<TravelSummary.Summary />
				<TravelSummary.Delay />
			</>
		),
	},
	parameters: {
		data: CAR_MOCK_NOW,
	},
};

export const Later: Story = {
	args: {
		children: (
			<>
				<TravelSummary.Summary />
			</>
		),
	},
	parameters: {
		data: {
			...CAR_ROUTE_MOCK,
			summary: {
				...CAR_ROUTE_MOCK.summary,
				arrival: format(addDays(new Date(), 1), "yyyy-MM-dd'T'HH:mm:ss.SSS"),
				departure: format(addDays(new Date(), 1), "yyyy-MM-dd'T'HH:mm:ss.SSS"),
			},
		},
	},
};

export const WithDelay: Story = {
	args: {
		children: (
			<>
				<TravelSummary.Summary />
				<TravelSummary.Delay />
			</>
		),
	},

	parameters: {
		data: {
			...CAR_MOCK_NOW,
			summary: {
				...CAR_MOCK_NOW.summary,
				delayInSeconds: 600,
			},
		},
	},
};

export const NoDelay: Story = {
	args: {
		children: (
			<>
				<TravelSummary.Summary />
				<TravelSummary.Delay />
			</>
		),
	},

	parameters: {
		data: {
			...CAR_MOCK_NOW,
			summary: {
				...CAR_MOCK_NOW.summary,
				delayInSeconds: 0,
			},
		},
	},
};
