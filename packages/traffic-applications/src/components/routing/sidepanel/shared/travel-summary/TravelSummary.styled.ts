import type { DefaultThemeProps } from "@anwb/poncho/design-tokens/theme";

import Typography from "@anwb/poncho/components/typography";
import { pxToRem } from "@anwb/poncho/design-tokens/style-utilities";
import styled, { css } from "styled-components";

const panelStyling = ({ theme }: DefaultThemeProps) => css`
	display: grid;
	grid-template-columns: 1fr 1px 1fr;

	gap: ${pxToRem(theme.spacing["200"])};
	background-color: ${theme.colors.extralight.bgPanelFeatured};
	padding: ${pxToRem(theme.spacing["400"])} ${pxToRem(theme.spacing["300"])};

	@media (min-width: ${pxToRem(theme.viewportBreakpoint.md)}) {
		padding: ${pxToRem(theme.spacing["300"])} ${pxToRem(theme.spacing["350"])}
			${pxToRem(theme.spacing["400"])} ${pxToRem(theme.spacing["350"])};
	}
`;

export const Container = styled.section(
	({ theme }: DefaultThemeProps) => css`
		${panelStyling};
		grid-template-areas:
			"travel divider arrival"
			"delay delay delay";

		@media (min-width: ${pxToRem(theme.viewportBreakpoint.md)}) {
			grid-template-areas:
				"travel divider arrival"
				"delay divider arrival";
		}
	`,
);

export const ContainerLater = styled.section(
	() => css`
		${panelStyling};
		grid-template-columns: 1fr 1px 1fr;
		grid-template-areas:
			"travel travel travel"
			"horizontal horizontal horizontal"
			"depart divider arrival";
	`,
);

export const TimeInfo = styled.div(
	({ theme }: DefaultThemeProps) => css`
		display: flex;
		flex-direction: column;
		grid-area: travel;

		@media (min-width: ${pxToRem(theme.viewportBreakpoint.md)}) {
			flex-direction: row;
			align-items: baseline;
			gap: ${pxToRem(theme.spacing["200"])};
		}
	`,
);

const dotSize = 4;

export const Distance = styled(Typography)(
	({ theme }: DefaultThemeProps) => css`
		@media (min-width: ${pxToRem(theme.viewportBreakpoint.md)}) {
			&:before {
				content: "";
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='4' height='4' viewBox='0 0 4 4' fill='none'%3E%3Ccircle cx='2' cy='2' r='2' fill='%23000307'/%3E%3C/svg%3E");
				background-repeat: no-repeat;
				background-size: cover;
				display: inline-block;
				width: ${dotSize}px;
				height: ${dotSize}px;
				border-radius: 50%;
				position: relative;
				top: -${pxToRem(dotSize)};
				margin-right: ${pxToRem(theme.spacing["200"])};
			}
		}
	`,
);

export const DelayInfo = styled(Typography)<{
	$hasDelay: boolean;
}>(
	({ $hasDelay, theme }: DefaultThemeProps & { $hasDelay: boolean }) => css`
		color: #ce6f00;
		font-style: normal;
		margin-bottom: 0;
		margin-top: 0;
		width: 100%;
		max-width: 300px;
		background-color: ${theme.colors.blanc.bgPanelStandard};
		padding: ${pxToRem(theme.spacing["100"])};
		border-radius: ${pxToRem(theme.borderRadius.s)};
		display: flex;
		grid-area: delay;

		.PONCHO-icon {
			margin-right: ${pxToRem(theme.spacing["100"])};
		}

		${!$hasDelay &&
		css`
			color: ${theme.colors.positive.textSupport};
		`}

		@media (min-width: ${pxToRem(theme.viewportBreakpoint.md)}) {
			width: max-content;
		}
	`,
);

export const Departure = styled.div(
	() => css`
		grid-area: depart;
	`,
);

export const Arrival = styled.div(
	({ theme }: DefaultThemeProps) => css`
		margin-inline-start: 0;
		grid-area: arrival;

		@media (min-width: ${pxToRem(theme.viewportBreakpoint.md)}) {
			grid-area: arrival;
		}
	`,
);

export const TimeTitle = styled(Typography)(() => css``);
export const Title = styled(Typography)(
	({ theme }: DefaultThemeProps) => css`
		color: ${theme.colors.informative.textDescription};
		margin-block-end: 0;
	`,
);

export const VerticalDivider = styled.div(
	({ theme }: DefaultThemeProps) => css`
		grid-area: divider;
		width: 1px;
		background-color: ${theme.colors.light.divider};
		height: 100%;
	`,
);

export const HorizontalDivider = styled.div(
	({ theme }: DefaultThemeProps) => css`
		width: 100%;
		background-color: ${theme.colors.light.divider};
		height: 1px;
		grid-area: horizontal;
	`,
);
