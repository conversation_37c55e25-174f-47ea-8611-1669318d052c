import type { DefaultThemeProps } from "@anwb/poncho/design-tokens/theme";

import Icon from "@anwb/poncho/components/icon";
import Typography from "@anwb/poncho/components/typography";
import { pxToRem } from "@anwb/poncho/design-tokens/style-utilities";
import styled, { css } from "styled-components";

export const Notification = styled.div(
	({ theme }: DefaultThemeProps) => css`
		display: flex;
		justify-content: space-between;
		align-items: baseline;
		padding: ${pxToRem(theme.spacing["300"])};
		background-color: ${theme.colors.warning.bgNotification};
		border-left: 2px solid ${theme.colors.warning.borderNotification};
	`,
);

export const Item = styled.div<{
	$colorLink?: boolean;
}>(
	({ $colorLink, theme }: DefaultThemeProps & { $colorLink?: boolean }) => css`
		display: flex;
		gap: ${pxToRem(theme.spacing["100"])};
		align-items: center;

		p {
			margin: 0;
			${$colorLink
				? `color: ${theme.colors.highlight.textLink} !important;
        text-decoration: underline;`
				: `color: ${theme.colors.base.textLabel} !important;`}
		}

		a {
			font-size: ${pxToRem(theme.fontSize["300"])};
		}

		button {
			background: none;
			border: none;
			padding: 0;
			font: inherit;
			cursor: pointer;
		}
	`,
);

export const IconColor = styled(Icon)(
	({ theme }: DefaultThemeProps) => css`
		color: ${theme.colors.warning.iconNotification};
	`,
);

export const Location = styled(Typography)(
	({ theme }: DefaultThemeProps) => css`
		font-size: ${pxToRem(theme.fontSize["400"])};
		display: flex;
		gap: ${pxToRem(theme.spacing["100"])};
		margin-bottom: ${pxToRem(theme.spacing["100"])};
	`,
);

export const Description = styled(Typography)(
	({ theme }: DefaultThemeProps) => css`
		margin-top: ${pxToRem(theme.spacing["100"])};
	`,
);

export const Date = styled(Typography)`
	font-weight: 600;
`;
