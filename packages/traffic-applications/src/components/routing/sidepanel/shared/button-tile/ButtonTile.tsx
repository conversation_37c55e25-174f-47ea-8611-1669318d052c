import type { IllustrativeIconsKey } from "@anwb/poncho/utilities/icon-utils";

import {
	TileActionArrow,
	TileActionText,
	TileActionTextWrapper,
} from "@anwb/poncho/components/tiles/components/Tile/styles/tile.styled";

import * as Styled from "./ButtonTile.styled";

export function ButtonTile({
	href,
	icon,
	onClick,
	title,
}: {
	href?: string;
	icon: IllustrativeIconsKey;
	onClick?: () => void;
	title: string;
}) {
	return (
		<TileContainer href={href} onClick={onClick}>
			<Styled.TileActionIconWrapper
				$layoutStyleOnMobile="rectangular"
				aria-hidden
				type="illustrative"
				variant={icon}
			/>
			<TileActionTextWrapper $layoutStyleOnMobile="rectangular">
				<TileActionText $variant="secondary">{title}</TileActionText>
				<TileActionArrow
					$variant="secondary"
					size="md"
					type="ui"
					variant="arrow-right"
				/>
			</TileActionTextWrapper>
		</TileContainer>
	);
}

function TileContainer({
	children,
	href,
	onClick,
}: {
	children: React.ReactNode;
	href?: string;
	onClick?: () => void;
}) {
	if (href) {
		return (
			<Styled.TileLink href={href} target="_blank">
				{children}
			</Styled.TileLink>
		);
	}
	return <Styled.TileButton onClick={onClick}>{children}</Styled.TileButton>;
}
