import type { DefaultThemeProps } from "@anwb/poncho/design-tokens/theme";

import { TileActionIcon } from "@anwb/poncho/components/tiles/components/Tile/styles/tile.styled";
import Typography from "@anwb/poncho/components/typography";
import { pxToRem } from "@anwb/poncho/design-tokens/style-utilities";
import styled, { css } from "styled-components";

export const Subtitle = styled(Typography)(
	({ theme }: DefaultThemeProps) => css`
		color: ${theme.colors.base.textSubtitles};
	`,
);
export const Title = styled(Typography)(
	({ theme }: DefaultThemeProps) => css`
		margin-bottom: ${pxToRem(theme.spacing["400"])};
	`,
);

export const Tiles = styled.div(
	({ theme }: DefaultThemeProps) => css`
		display: flex;
		flex-direction: column;
		gap: ${pxToRem(theme.spacing["300"])};
	`,
);

const TileStyling = ({ theme }: DefaultThemeProps) => css`
	display: flex;
	align-items: center;
	border: none;
	cursor: pointer;
	text-align: left;
	text-decoration: none;
	border-radius: ${pxToRem(theme.borderRadius.m)};
	width: 100%;
	height: 76px;
	box-sizing: border-box;
	gap: ${pxToRem(theme.spacing["300"])};
	padding: ${pxToRem(theme.spacing["300"])};
	background: ${theme.colors.extralight.bgTileSecondary};
	color: ${theme.colors.base.textButton};

	&:hover {
		background: ${theme.colors.light.bgTileSecondaryHover};
		color: ${theme.colors.base.textButton};
	}

	&:active {
		background: ${theme.colors.light.bgTileSecondaryActive};
	}

	&:focus {
		color: ${theme.colors.base.textButton};
	}

	&:focus-visible {
		border-radius: ${pxToRem(theme.borderRadius.m)};
		outline: ${theme.borderWidth.m}px solid
			${theme.colors.highlight.borderFocus};
		outline-offset: ${theme.borderWidth.m}px;
	}
`;

export const TileButton = styled.button`
	${TileStyling}
`;

export const TileLink = styled.a`
	${TileStyling}
`;

export const TileActionIconWrapper = styled(TileActionIcon)`
	width: 44px;
	height: 44px;
`;
