import type { <PERSON>a, StoryObj } from "@storybook/react";

import { expect, within } from "@storybook/test";

import { StoryContainer } from "../../../../../utils/utils-storybook";
import { ButtonTile } from "./ButtonTile";

const meta: Meta<typeof ButtonTile> = {
	component: ButtonTile,
	decorators: [
		(Story, { args }) => (
			<StoryContainer width={300}>
				<Story {...args} />
			</StoryContainer>
		),
	],
	parameters: {
		layout: "centered",
	},
	title: "Traffic/Routing/Sidepanel/ButtonTile",
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
	args: {
		href: "https://www.anwb.nl",
		icon: "gasstation-medium",
		onClick: () => {
			console.log("Button clicked");
		},
		title: "Brandstofkosten",
	},
	play: async ({ canvasElement }) => {
		const canvas = within(canvasElement);
		await expect(canvas.getByText("Benzinekosten")).toBeInTheDocument();
	},
};
