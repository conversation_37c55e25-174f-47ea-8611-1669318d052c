import Typography from "@anwb/poncho/components/typography";

import { ButtonTile } from "../button-tile/ButtonTile";
import * as Styled from "./TravelCost.styled";

export function TravelCost({ children }: { children?: React.ReactNode }) {
	return (
		<Styled.Wrapper>
			<Typography tagName="h3" variant="component-title">
				Reiskosten
			</Typography>
			<Styled.Container>{children}</Styled.Container>
		</Styled.Wrapper>
	);
}

function TankStationCost() {
	return (
		<ButtonTile
			icon="gasstation-medium"
			onClick={() => {
				console.log("todo");
			}}
			title="Brandstofkosten"
		/>
	);
}

function ParkingCost() {
	return (
		<ButtonTile
			icon="parking"
			onClick={() => {
				console.log("todo");
			}}
			title="Parkeerkosten"
		/>
	);
}

TravelCost.TankStation = TankStationCost;
TravelCost.Parking = ParkingCost;
