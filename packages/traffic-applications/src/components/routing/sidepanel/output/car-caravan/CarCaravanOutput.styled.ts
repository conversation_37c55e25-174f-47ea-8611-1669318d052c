import type { DefaultThemeProps } from "@anwb/poncho/design-tokens/theme";

import { pxToRem } from "@anwb/poncho/design-tokens/style-utilities";
import styled, { css } from "styled-components";

export const Actions = styled.div(
	({ theme }: DefaultThemeProps) => css`
		display: flex;
		flex-wrap: wrap;
		gap: ${pxToRem(theme.spacing["400"])};
	`,
);

export const Divider = styled.div(
	({ theme }: DefaultThemeProps) => css`
		margin: ${pxToRem(theme.spacing["500"])} 0;
		height: 1px;
		background-color: ${theme.colors.light.divider};
		width: 100%;
	`,
);
