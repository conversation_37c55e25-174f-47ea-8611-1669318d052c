import { ButtonTertiary } from "@anwb/poncho/components/button";
import Link from "@anwb/poncho/components/link";

import { CONTENT_PAGES } from "../../../../../utils/contentPageRegistry";
import { useTravelNow } from "../../../hooks/use-active-route.hook";
import { CarCrossSellEU, CarCrossSellNL } from "../../cross-sell/CrossSell";
import { useInputNonNLWaypoints } from "../../input/hooks/use-input-waypoints.hook";
import { PrintButton } from "../../shared/print-button/PrintButton";
import { RoutingNotification } from "../../shared/routing-notification/RoutingNotification";
import { SaveRouteButton } from "../../shared/save-route-button/SaveRouteButton";
import { TravelCost } from "../../shared/travel-cost/TravelCost";
import { TravelSummary } from "../../shared/travel-summary/TravelSummary";
import { OutputContent } from "../OutputContent";
import * as Styled from "./CarCaravanOutput.styled";
import {
	pushGADataRoadworksEu,
	pushGADataRoadworksPortal,
} from "./utils/CarCaravanOutput.ga";

export default function CarCaravanOutput() {
	const isNotNLRoute = useInputNonNLWaypoints();
	const leavingToday = useTravelNow();
	const isAbroadRoute = isNotNLRoute.length > 0;
	const isAbroadRouteToday = isAbroadRoute && leavingToday;
	const travelInformationAbroad = isAbroadRouteToday
		? "Verkeersinformatie buitenland"
		: "Werkzaamheden buitenland";

	const travelInformationAbroadLink = isAbroadRouteToday
		? CONTENT_PAGES.relevantLinks.trafficInfo
		: CONTENT_PAGES.relevantLinks.trafficRoadworksEu;

	return (
		<OutputContent>
			<TravelSummary>
				<TravelSummary.Summary />
				<TravelSummary.Delay />
			</TravelSummary>

			<OutputContent.Body>
				<RoutingNotification />
				<Styled.Actions>
					<SaveRouteButton />
					<ButtonTertiary>Navigeren</ButtonTertiary>
				</Styled.Actions>

				<Styled.Actions>
					<PrintButton />
					<Link icon="route" iconInverted>
						Routebeschrijving
					</Link>
					{isAbroadRoute && (
						<Link
							href={travelInformationAbroadLink}
							onClick={() => {
								if (isAbroadRouteToday) {
									pushGADataRoadworksPortal();
								} else {
									pushGADataRoadworksEu();
								}
							}}
							rel="noopener noreferrer"
							target="_blank"
						>
							{travelInformationAbroad}
						</Link>
					)}
				</Styled.Actions>

				<Styled.Divider />

				<TravelCost>
					<TravelCost.TankStation />
					{!isAbroadRoute && <TravelCost.Parking />}
				</TravelCost>
			</OutputContent.Body>

			<OutputContent.Footer>
				{!isAbroadRoute ? <CarCrossSellNL /> : <CarCrossSellEU />}
			</OutputContent.Footer>
		</OutputContent>
	);
}
