import { ButtonIcon } from "@anwb/poncho/components/button";
import Icon from "@anwb/poncho/components/icon";
import { VisuallyHidden } from "@anwb/poncho/components/visually-hidden";
import { Fragment } from "react/jsx-runtime";

import { useActiveRoute } from "../../context/active-route-context/active-route.context";
import { useInput } from "../../hooks/use-input.hook";
import { useRoutingMapActions } from "../../map/RoutingMap.store";
import { useWaypoints } from "../input/hooks/use-input-waypoints.hook";
import { routeTypeLabel } from "../input/types/input.type.utils";
import * as Styled from "./RoutingEdit.styled";
import { getRouteCityNames } from "./RoutingEdit.utils";

export function RoutingEdit() {
	const { editRoute } = useRoutingMapActions();
	const input = useInput();
	const activeRoute = useActiveRoute();
	const route = useWaypoints();

	if (!activeRoute) {
		return null;
	}

	return (
		<Styled.Route>
			<article>
				<RouteTitle cityNames={getRouteCityNames(route)} />
				<Styled.RouteType>
					<Icon aria-hidden variant="car" />
					<Styled.RouteOption tagName="span" variant="component-subtitle">
						{routeTypeLabel(input)}
					</Styled.RouteOption>
				</Styled.RouteType>
			</article>
			<ButtonIcon
				color="tertiary"
				icon="edit"
				onClick={editRoute}
				title="Route bewerken"
				variant="on-dark"
			>
				<VisuallyHidden>Route bewerken</VisuallyHidden>
			</ButtonIcon>
		</Styled.Route>
	);
}

/** Show a list of locations for the route separated by an arrow. */
function RouteTitle({ cityNames }: { cityNames: Array<string> }) {
	return (
		<Styled.Title tagName="h2" variant="component-title">
			<VisuallyHidden>{cityNames.join(" naar ")}</VisuallyHidden>
			<span aria-hidden="true">
				{cityNames.map((city, index) => (
					<Fragment key={`${city}-${index}`}>
						{index !== 0 && <Separator />}
						{city}
					</Fragment>
				))}
			</span>
		</Styled.Title>
	);
}

function Separator() {
	return <Styled.TitleIcon size="sm" type="ui" variant="arrow-right" />;
}
