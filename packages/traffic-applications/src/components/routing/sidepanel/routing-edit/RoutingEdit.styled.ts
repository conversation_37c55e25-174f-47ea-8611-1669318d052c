import type { DefaultThemeProps } from "@anwb/poncho/design-tokens/theme";

import Icon from "@anwb/poncho/components/icon";
import Typography from "@anwb/poncho/components/typography";
import { pxToRem } from "@anwb/poncho/design-tokens/style-utilities";
import styled, { css } from "styled-components";

export const Route = styled.section(
	({ theme }: DefaultThemeProps) => css`
		background-color: ${theme.colors.base.bgCardReview};
		display: flex;
		padding: ${pxToRem(theme.spacing["400"])} ${pxToRem(theme.spacing["300"])};
		color: ${theme.colors.blanc.textInverse};
		justify-content: space-between;
		align-items: flex-start;
		height: 100px;
		min-height: max-content;
	`,
);

export const RouteType = styled.p(
	({ theme }: DefaultThemeProps) => css`
		display: flex;
		gap: ${pxToRem(theme.spacing["100"])};
		margin-top: ${pxToRem(theme.spacing["100"])};
		margin-bottom: 0;
	`,
);

export const RouteOption = styled(Typography)(
	({ theme }: DefaultThemeProps) => css`
		color: ${theme.colors.blanc.textInverse};
	`,
);

export const Title = styled(Typography)(
	({ theme }: DefaultThemeProps) => css`
		margin: 0 0 ${pxToRem(theme.spacing["100"])} 0;
		color: ${theme.colors.blanc.textInverse};
		align-items: center;
		max-width: 350px;
	`,
);

export const TitleIcon = styled(Icon)(
	({ theme }: DefaultThemeProps) => css`
		margin: 0 ${pxToRem(theme.spacing["100"])};
		vertical-align: middle;
		color: ${theme.colors.blanc.textInverse};
	`,
);
