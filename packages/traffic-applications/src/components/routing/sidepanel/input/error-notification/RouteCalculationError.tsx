import { RouteCalculationError as RouteError } from "../../../../../api/routing/routing-input-api";
import { useRouteQuery } from "../../../../../api/routing/routing-input.queries";
import { useInputUrlState } from "../../../context/input-url-state/InputUrlState.context";
import { useInputWaypoints } from "../hooks/use-input-waypoints.hook";
import { ErrorNotification } from "./ErrorNotification";

/** Listens for errors in the route query and displays a notification */
export function RouteCalculationError() {
	const { urlState } = useInputUrlState();
	const { data: waypoints } = useInputWaypoints();
	const { error, isError } = useRouteQuery();

	return (
		<ErrorNotification
			error={error instanceof RouteError ? error : undefined}
			isError={isError}
			transportMode={urlState.transportMode}
			waypoints={waypoints}
		/>
	);
}
