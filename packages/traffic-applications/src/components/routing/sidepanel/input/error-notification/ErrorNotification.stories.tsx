import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { expect, within } from "@storybook/test";

import type { ApiErrorResponse } from "../../../../../api/api-response";

import {
	WAYPOINT_EDE_NLD,
	WAYPOINT_PARIS_FRA,
	WAYPOINT_ROTTERDAM_NLD,
} from "../../../../../api/geocoder/geocoder.api.mock";
import { transportMode } from "../../../../../api/transport-mode";
import { StoryContainer } from "../../../../../utils/utils-storybook";
import { ErrorNotification } from "./ErrorNotification";

const meta: Meta<typeof ErrorNotification> = {
	args: {
		error: undefined,
		isError: true,
		transportMode: transportMode.Car,
		waypoints: [WAYPOINT_EDE_NLD, WAYPOINT_ROTTERDAM_NLD],
	},
	component: ErrorNotification,
	decorators: [
		(Story, { parameters }) => (
			<StoryContainer width={440}>
				<Story {...parameters} />
			</StoryContainer>
		),
	],
	parameters: {
		layout: "centered",
	},
	title: "Traffic/Routing/ErrorNotification",
};

export default meta;
type Story = StoryObj<typeof meta>;

const BAD_REQUEST: ApiErrorResponse = {
	error: {
		code: "BAD_REQUEST",
		message:
			"Een plezier/toeristische route is alleen mogelijk voor routes korter dan 900 kilometer. Pas de route aan en probeer het nog een keer.",
		title: "Route niet mogelijk",
	},
};

export const NetworkError: Story = {
	play: async ({ canvasElement }) => {
		const canvas = within(canvasElement);
		await expect(
			canvas.getByText("Er is iets mis gegaan bij het plannen van de route"),
		).toBeInTheDocument();
		await expect(
			canvas.getByText(
				"Pas de routeopties aan of probeer het later nog een keer.",
			),
		).toBeInTheDocument();
	},
};

export const ErrorFromApi: Story = {
	args: {
		data: BAD_REQUEST,
		isError: false,
		transportMode: transportMode.Car,
		waypoints: [WAYPOINT_EDE_NLD, WAYPOINT_ROTTERDAM_NLD],
	},
	play: async ({ canvasElement }) => {
		const canvas = within(canvasElement);
		await expect(canvas.getByText("Route niet mogelijk")).toBeInTheDocument();
		await expect(
			canvas.getByText(
				"Een plezier/toeristische route is alleen mogelijk voor routes korter dan 900 kilometer. Pas de route aan en probeer het nog een keer.",
			),
		).toBeInTheDocument();
	},
};

export const AndesNonNLError: Story = {
	args: {
		data: BAD_REQUEST,
		isError: false,
		transportMode: transportMode.Bike,
		waypoints: [WAYPOINT_EDE_NLD, WAYPOINT_PARIS_FRA],
	},
	play: async ({ canvasElement }) => {
		const canvas = within(canvasElement);
		await expect(
			canvas.getByText(
				"Fiets- en wandelroutes kunnen alleen binnen Nederland en naar bepaalde plaatsen dicht over de grens gepland worden.",
			),
		).toBeInTheDocument();
	},
};
