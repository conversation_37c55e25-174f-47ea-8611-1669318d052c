import Notification from "@anwb/poncho/components/notification";
import { useEffect } from "react";

import type { Waypoint } from "../../../../../api/geocoder/geocoder.types";

import { RouteCalculationError } from "../../../../../api/routing/routing-input-api";
import { type TransportMode } from "../../../../../api/transport-mode";
import { pushGADataPlanRouteError } from "../../../pushGaDataLayer";
import {
	andesForeignError,
	genericErrorMessage,
	genericErrorTitle,
} from "./ErrorNotification.constant";
import { hasForeignWaypoints, isAndesRoute } from "./ErrorNotification.utils";

type Props = {
	error: RouteCalculationError | undefined;
	isError: boolean;
	transportMode: TransportMode;
	waypoints?: Array<undefined | Waypoint>;
};

export function ErrorNotification({
	error,
	isError,
	transportMode,
	waypoints,
}: Props) {
	const hasAndesForeignError =
		isAndesRoute(transportMode) && hasForeignWaypoints(waypoints);

	if (error instanceof RouteCalculationError) {
		return (
			<Error
				description={hasAndesForeignError ? andesForeignError : error.message}
				title={error.title}
			/>
		);
	}

	// Network error
	if (isError) {
		return <Error />;
	}

	return null;
}

function Error({
	description,
	title,
}: {
	description?: string;
	title?: string;
}) {
	useEffect(() => {
		window.scroll({ behavior: "smooth", left: 0, top: 0 });
		pushGADataPlanRouteError();
	}, [title, description]);

	return (
		<Notification
			closeButton
			data-test-id="routing-input-error-notification"
			title={title ?? genericErrorTitle}
			variant="error"
		>
			{description ?? genericErrorMessage}
		</Notification>
	);
}

Error.displayName = "ErrorNotificationError";
