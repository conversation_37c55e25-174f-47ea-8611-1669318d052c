import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useQueryClient } from "@tanstack/react-query";
import { useEffect } from "react";
import { FormProvider, useForm, type UseFormReturn } from "react-hook-form";

import {
	getRouting<PERSON><PERSON><PERSON><PERSON><PERSON>,
	useRouteQuery,
} from "../../../../api/routing/routing-input.queries";
import { transportMode } from "../../../../api/transport-mode";
import { useInputUrlState } from "../../context/input-url-state/InputUrlState.context";
import { useMapUrlState } from "../../context/map-url-state/MapUrlState.context";
import { useRoutingMapActions } from "../../map/RoutingMap.store";
import { BreadCrumbs } from "./breadcrumbs/BreadCrumbs";
import { CarForm } from "./car-form/CarForm";
import { RouteCalculationError } from "./error-notification/RouteCalculationError";
import {
	useInputWaypoints,
	waypointCache<PERSON>ey,
} from "./hooks/use-input-waypoints.hook";
import * as Styled from "./Input.styled";
import { TransportModePicker } from "./shared/transport-mode-picker/TransportModePicker";
import {
	type InputForm,
	InputSchema,
	type InputUrlState,
	validateFormSchema,
} from "./types/input.schema";
import { useMigrateDeprecatedWaypointParams } from "./utils/deprecated-waypoint-params/use-migrate-deprecated-waypoint-params.hook";
import { updateRecentSearchesInLocalstorage } from "./utils/recent-searches-localstorage/recents-searches-localstorage";
import { WaypointsList } from "./waypoint-list/WaypointsList";
import {
	fieldArrayToWaypoints,
	waypointsToFieldArray,
} from "./waypoint-list/WaypointsList.utils";

export function RoutingInput() {
	const { routeSubmitted } = useRoutingMapActions();
	const { setUrlState, urlState } = useInputUrlState();
	const queryClient = useQueryClient();
	const { setState } = useMapUrlState();

	/**
	 * Initially the form has 2 empty waypoints, if there are lat/lng query
	 * parameters provided in the URL, those will be reversed geocoded and
	 * asynchronously filled in.
	 */
	const form = useForm<InputForm>({
		defaultValues: {
			...urlState,
			waypoints: [{ waypoint: undefined }, { waypoint: undefined }],
		},
		resolver: zodResolver<InputForm>(validateFormSchema),
	});

	const transportModeValue = form.watch("transportMode");

	const { data: waypoints } = useInputWaypoints();

	/** When waypoints from the url have loaded, fill the waypoints inputs. */
	useEffect(() => {
		if (waypoints && waypoints.length > 0) {
			form.setValue("waypoints", waypointsToFieldArray(waypoints));
		}
	}, [waypoints]);

	/**
	 * Correct the URL when a content page links to the routeplanner using
	 * deprecated query parameters.
	 */
	useMigrateDeprecatedWaypointParams();

	// If the transport mode is changed, we need to reset the form validation strategy
	const selectedTransportMode = form.watch("transportMode");

	useEffect(() => {
		resetValidationStrategyToOnSubmit(form);
	}, [selectedTransportMode]);

	/** Submit the form and update the URL state with the form values. */
	const onSubmit = async (value: InputForm) => {
		const waypoints = fieldArrayToWaypoints(value.waypoints);
		// Store the autocompleted Waypoint objects in the query client cache, so we
		// won't trigger a unnecessary geocoding request in the useInputWaypoints().
		queryClient.setQueryData(waypointCacheKey(waypoints), waypoints);

		const urlState: Partial<InputUrlState> = { ...value, waypoints };

		// Clear this route from cache, (if it exists). This is required if there was a network error
		// or the user wants the same route to be recalculated for the current moment in time.
		await queryClient.invalidateQueries({
			queryKey: getRoutingQueryKey(InputSchema.safeParse(urlState).data),
		});

		// Update the page URL with the new query parameters
		await setUrlState(urlState);
		await setState({
			activeRoute: 0,
		});
		updateRecentSearchesInLocalstorage(waypoints);

		requestAnimationFrame(() => {
			resetValidationStrategyToOnSubmit(form);
		});

		routeSubmitted();
	};

	return (
		<FormProvider {...form}>
			{/* eslint-disable-next-line @typescript-eslint/no-misused-promises */}
			<Styled.Form onSubmit={form.handleSubmit(onSubmit)}>
				<BreadCrumbs />
				<Styled.Title tagName="h1">Routeplanner</Styled.Title>
				<RouteCalculationError />

				<TransportModePicker />

				<WaypointsList />

				<Styled.Filters>
					{transportModeValue === transportMode.Car && <CarForm />}
					{transportModeValue === transportMode.Caravan && <CarForm />}
				</Styled.Filters>

				<SubmitButton />
			</Styled.Form>
		</FormProvider>
	);
}

function SubmitButton() {
	const { data: route, isFetching } = useRouteQuery();

	return (
		<Styled.SubmitButton icon={null} loading={isFetching} type="submit">
			{isFetching
				? "Route wordt berekend"
				: route
					? "Herbereken route"
					: "Plan route"}
		</Styled.SubmitButton>
	);
}

/**
 * React Hook Form has by default two distinct validation strategies:
 *
 * - Before submission: "onSubmit" mode (validation runs only on submit).
 *
 *   - Docs: https://react-hook-form.com/docs/useform#mode
 * - After submission: "onChange" mode (re-validation on change).
 *
 *   - Docs: https://react-hook-form.com/docs/useform#reValidateMode
 *
 * Preferred behavior:
 *
 * - Use "onSubmit" validation initially.
 * - After a failed submission, switch to "onChange" for immediate feedback.
 * - After a successful submission, revert to "onSubmit" to prevent unnecessary
 *   validation errors.
 *
 * To achieve this, we reset `keepSubmitCount` on successful submission.
 */
const resetValidationStrategyToOnSubmit = (form: UseFormReturn<InputForm>) => {
	form.reset(undefined, {
		keepSubmitCount: false, // This makes sure we use the onSubmit validation strategy instead of the onChange validation strategy
		keepValues: true,
	});
};
