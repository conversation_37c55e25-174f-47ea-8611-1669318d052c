import mockdate from "mockdate";
import { describe, expect, it } from "vitest";

import { dateTimeToHumanReadableFormat } from "./DateTimeOptions.utils";

/**
 * Normally you would not test third party implementations, but this test
 * functions as documentation.
 */
describe("roundTimeOnQuarterHours", () => {
	// Mock the current date to ensure consistent test results
	mockdate.set("1988-09-19T00:00:00.000Z");

	it("should return nothing in case of undefined", () => {
		expect(dateTimeToHumanReadableFormat(undefined)).toBe("Ongeldige datum");
	});
	it("should return nothing in case of an empty string", () => {
		expect(dateTimeToHumanReadableFormat("1988-09-...invalid date")).toBe(
			"Ongeldige datum",
		);
	});
	it("should only return the time if the date is today", () => {
		expect(dateTimeToHumanReadableFormat("1988-09-19T13:37:00.000Z")).toBe(
			"15:37",
		);
	});
	it("Should convert the date for tomorrow", () => {
		expect(dateTimeToHumanReadableFormat("1988-09-20T13:37:00.000Z")).toBe(
			"morgen 15:37",
		);
	});

	it("Should convert the date for in 2 days", () => {
		expect(dateTimeToHumanReadableFormat("1988-09-21T13:37:00.000Z")).toBe(
			"woensdag 21 sep. 15:37",
		);
	});

	it("should include the month if the date is next month", () => {
		expect(dateTimeToHumanReadableFormat("1988-10-21T13:37:00.000Z")).toBe(
			"vrijdag 21 okt. 14:37",
		);
	});
});
