import type { Icon<PERSON><PERSON>t<PERSON><PERSON> } from "@anwb/poncho/utilities/icon-utils";

import TagIcon from "@anwb/poncho/components/tags/components/TagIcon/TagIcon";
import { useFormContext } from "react-hook-form";

import type { TransportMode } from "../../../../../../../api/transport-mode";
import type { InputForm } from "../../../types/input.schema";

import * as Styled from "./TransportModeTag.styled";

export function TransportModeTag({
	content,
	icon,
	mode,
	onClick,
}: {
	content: string;
	icon: IconVariantKey;
	mode: TransportMode;
	onClick: (mode: TransportMode) => void;
}) {
	const { watch } = useFormContext<InputForm>();
	const value = watch("transportMode");

	return (
		<Styled.ButtonTag
			htmlFor={`routing-input-transportmode-${mode}`}
			onClick={() => {
				onClick(mode);
			}}
			title={content}
		>
			<input
				checked={value === mode}
				id={`routing-input-transportmode-${mode}`}
				name="transportMode"
				readOnly
				type="radio"
				value={mode}
			/>
			<TagIcon aria-hidden={true} size="xl" variant={icon} />
			<Styled.Label tagName="span">{content}</Styled.Label>
		</Styled.ButtonTag>
	);
}
