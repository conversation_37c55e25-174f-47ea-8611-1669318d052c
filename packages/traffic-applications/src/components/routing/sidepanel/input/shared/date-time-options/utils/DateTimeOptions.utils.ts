import {
	differenceInCalendarDays,
	format,
	formatRelative,
	isValid,
	parse,
	roundToNearestMinutes,
} from "date-fns";
import { nl } from "date-fns/locale";

import { timeType, type TimeType } from "../../../types/input.types";

/**
 * Generate the time at the nearest 15 minutes from now.
 *
 * @example
 * 	0, 15, 20, 30, 45;
 */
export const getDefaultDateTimeValue = () =>
	roundToNearestMinutes(new Date(), {
		nearestTo: 15,
		roundingMethod: "ceil",
	}).toISOString();

/** This will display a date time string */
export const dateTimeToHumanReadableFormat = (value: string | undefined) => {
	const now = new Date();

	if (!value) {
		return "Ongeldige datum";
	}

	const dateTime = parse(value, "yyyy-MM-dd'T'HH:mm:ss.SSSX", now);
	const diffDays = differenceInCalendarDays(dateTime, now);

	if (!isValid(dateTime)) {
		return "Ongeldige datum";
	}

	if (diffDays === 0) {
		return format(dateTime, "HH:mm", { locale: nl });
	} else if (diffDays === 1) {
		return `morgen ${format(dateTime, "HH:mm", { locale: nl })}`;
	} else if (diffDays === -1) {
		// translate yesterdays date in locale
		return formatRelative(dateTime, now, { locale: nl });
	} else {
		return format(dateTime, "EEEE d MMM HH:mm", { locale: nl });
	}
};

/** Get the next form value based on the selected time type. */
export const getDateTimeOptionsValue = (
	timeTypeValue: TimeType,
	departAt: string | undefined,
	arriveAt: string | undefined,
) => {
	switch (timeTypeValue) {
		case timeType.arrive:
			return {
				arriveAt: departAt ?? getDefaultDateTimeValue(),
				departAt: undefined,
			};
		case timeType.depart:
			return {
				arriveAt: undefined,
				departAt: arriveAt ?? getDefaultDateTimeValue(),
			};
		case timeType.now:
			return {
				arriveAt: undefined,
				departAt: undefined,
			};
	}
};
