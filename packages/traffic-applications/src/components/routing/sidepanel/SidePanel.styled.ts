import type { DefaultThemeProps } from "@anwb/poncho/design-tokens/theme";

import { pxToRem } from "@anwb/poncho/design-tokens/style-utilities";
import styled, { css, keyframes } from "styled-components";

import { routingGridAreas } from "../Routing.styled";

export const BlueContainer = styled.div(
	({ theme }: DefaultThemeProps) => css`
		background: ${theme.colors.base.bgCardReview};
		position: relative;
		z-index: 1;

		& > * {
			animation: ${fadeIn} 0.5s forwards ease-out;
			animation-fill-mode: both;
		}
	`,
);

const fadeIn = keyframes`
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
`;

export const Content = styled.div(
	({ theme }: DefaultThemeProps) => css`
		grid-area: ${routingGridAreas.content};
		padding: ${pxToRem(theme.spacing["400"])} ${pxToRem(theme.spacing["300"])};

		@media (min-width: ${pxToRem(theme.viewportBreakpoint.md)}) {
			padding: ${pxToRem(theme.spacing["400"])};
		}

		& > * + * {
			margin-top: ${pxToRem(theme.spacing["500"])};
		}
	`,
);

export const Placeholder = styled.div(
	({ theme }: DefaultThemeProps) => css`
		padding: ${pxToRem(theme.spacing["400"])} ${pxToRem(theme.spacing["300"])};

		& > * + * {
			margin-top: ${pxToRem(theme.spacing["300"])};
		}
	`,
);
