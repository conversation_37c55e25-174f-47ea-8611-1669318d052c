import { SkeletonRectangle } from "@anwb/poncho/components/skeleton";
import { useEffect, useRef } from "react";

import { useRouteQuery } from "../../../api/routing/routing-input.queries";
import { transportMode } from "../../../api/transport-mode";
import { useActiveRoute } from "../context/active-route-context/active-route.context";
import { useHasRoute } from "../hooks/use-active-route.hook";
import { useGaPlannedRouteSuccess } from "../hooks/use-ga-planned-route-success.hook";
import { useShowForm } from "../map/RoutingMap.store";
import { FeedbackLinks } from "./feedback-links/FeedbackLinks";
import { RoutingInput } from "./input/RoutingInput";
import CarCaravanOutput from "./output/car-caravan/CarCaravanOutput";
import { PromoBanner } from "./promo-banner/PromoBanner";
import { RelevantLinks } from "./relevant-links/RelevantLinks";
import { RoutingEdit } from "./routing-edit/RoutingEdit";
import * as Styled from "./SidePanel.styled";

export function SidePanel() {
	const hasActiveRoute = useHasRoute();
	const { isFetched, isFetching } = useRouteQuery();

	useGaPlannedRouteSuccess();

	return (
		<>
			<BlueContainer />
			{isFetching ? (
				<RouteContentPlaceholder />
			) : hasActiveRoute && isFetched ? (
				<RouteContent />
			) : (
				<NoRouteContent />
			)}
		</>
	);
}

export function BlueContainer() {
	const showForm = useShowForm();
	const { isError, isFetching } = useRouteQuery();
	const containerRef = useRef<HTMLDivElement>(null);

	const showInputForm = isError || isFetching || showForm;

	useEffect(() => {
		if (!showInputForm) {
			containerRef.current?.scrollIntoView({
				behavior: "smooth",
				block: "start",
			});
		}
	}, [showInputForm]);

	return (
		<Styled.BlueContainer ref={containerRef}>
			{showInputForm ? <RoutingInput /> : <RoutingEdit />}
		</Styled.BlueContainer>
	);
}

export function NoRouteContent() {
	return (
		<Styled.Content>
			<PromoBanner />
			<RelevantLinks />
			<FeedbackLinks />
		</Styled.Content>
	);
}

export function RouteContent() {
	const activeRoute = useActiveRoute();
	const transportModeValue = activeRoute?.transportMode;

	return (
		<>
			{(transportModeValue === transportMode.Car ||
				transportModeValue === transportMode.Caravan) && <CarCaravanOutput />}
		</>
	);
}

function RouteContentPlaceholder() {
	return (
		<Styled.Placeholder>
			<SkeletonRectangle height="100px" width="100%" />
			<SkeletonRectangle height="40px" width="100%" />
			<SkeletonRectangle height="50px" width="100%" />
			<SkeletonRectangle height="50px" width="100%" />
		</Styled.Placeholder>
	);
}
