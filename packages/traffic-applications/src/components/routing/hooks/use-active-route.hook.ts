import { addMinutes, isWithinInterval, parseISO } from "date-fns";

import { convertDuration } from "../../../utils/utils";
import { useActiveRoute } from "../context/active-route-context/active-route.context";

export const useHasRoute = (): boolean => {
	const activeRoute = useActiveRoute();
	return Boolean(activeRoute);
};

export const useTravelTime = (): null | string => {
	const activeRoute = useActiveRoute();

	if (!activeRoute) {
		return null;
	}

	return convertDuration(activeRoute.summary.durationInSeconds);
};

/**
 * @todo This hook should be checking the input schema departureAt and arriveAt
 *   fields But because of unsupported state in storybook we check the active
 *   route
 */
export const useTravelNow = (): boolean => {
	const activeRoute = useActiveRoute();

	if (!activeRoute) {
		return false;
	}

	const departureDate = parseISO(activeRoute.summary.departure);
	const now = new Date();

	return isWithinInterval(now, {
		end: addMinutes(departureDate, 15),
		start: departureDate,
	});
};
