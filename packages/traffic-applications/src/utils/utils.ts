import {
	differenceInCalendarDays,
	format,
	formatRelative,
	isSameDay,
	parseISO,
} from "date-fns";
import { nl } from "date-fns/locale";

/** Check if a string contains any of the specified substrings. */
export const stringContains = (haystack: string, needles: Array<string>) =>
	needles.some((needle) => haystack.includes(needle));

/** Remove all the keys from the provided object that have a value of null */
export const removeNulls = <T extends Record<string, unknown>>(obj: T): T =>
	Object.fromEntries(
		Object.entries(obj).filter(([, value]) => value !== null),
	) as T;

/** Convert a duration in seconds to a human-readable text */
export const convertDuration = (
	duration: number,
	hourLabel = " uur,",
	minuteLabel = " min",
): string => {
	const hours = Math.floor(duration / 3600);
	const minutes = Math.floor((duration % 3600) / 60);

	if (hours >= 1) {
		return `${hours}${hourLabel} ${minutes}${minuteLabel}`;
	}

	return `${minutes}${minuteLabel}`;
};

/** This will convert a distance in meters to a string of text */
export const convertDistance = (
	distance: number,
	fulltext?: boolean,
): string => {
	const suffixes = {
		kilometer: fulltext ? "kilometer" : "km",
		meter: fulltext ? "meter" : "m",
	};

	if (distance === 0) {
		return "";
	} else if (distance < 100) {
		return `${distance > 2 ? Math.round(distance / 5) * 5 : distance} ${suffixes.meter}`;
	} else if (distance < 1000) {
		return `${Math.round(distance / 10) * 10} ${suffixes.meter}`;
	} else {
		return `${Math.round(distance / 1000).toLocaleString("nl-NL")} ${suffixes.kilometer}`;
	}
};

/** Capitalize the first letter of a string and lowercase the rest. */
export const capitalize = (value: string): string => {
	return value.charAt(0).toUpperCase() + value.slice(1).toLowerCase();
};

/** This will convert the provided date time to a readable dutch string */
export const convertDateTime = (dateTime: string): string => {
	const date = parseISO(dateTime);
	const now = new Date();
	const diffDays = differenceInCalendarDays(date, now);

	if (isSameDay(date, now)) {
		return format(date, "HH:mm", { locale: nl });
	} else if (diffDays === -1 || diffDays === 1) {
		return formatRelative(date, now, { locale: nl });
	}
	return format(date, "EEEEEE d MMM HH:mm", { locale: nl });
};

/** This will convert seconds to minutes */
export const convertSecondsToMinutes = (delay: number): number =>
	Math.floor(delay / 60);

/** This will convert converted time to screen reader only format */
export const convertTimeAriaLabel = (dateTime: string): string =>
	dateTime.split(":").join(" uur ") + " minuten";
