import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";

import {
	capitalize,
	convertDateTime,
	convertDistance,
	convertDuration,
	convertSecondsToMinutes,
	convertTimeAriaLabel,
	removeNulls,
	stringContains,
} from "./utils";

describe("utils", () => {
	describe("stringContains", () => {
		it("returns true if haystack contains any of the needles", () => {
			expect(stringContains("hello world", ["world", "foo"])).toBe(true);
		});

		it("returns false if haystack contains none of the needles", () => {
			expect(stringContains("hello world", ["foo", "bar"])).toBe(false);
		});

		it("returns false if needles array is empty", () => {
			expect(stringContains("hello world", [])).toBe(false);
		});

		it("returns true if haystack contains all of the needles", () => {
			expect(stringContains("hello world", ["hello", "world"])).toBe(true);
		});
	});

	describe("removeNullKeys", () => {
		it("removes keys with null values", () => {
			expect(removeNulls({ a: 1, b: null, c: 2 })).toEqual({ a: 1, c: 2 });
		});
		it("keeps keys with falsy but not null/undefined values", () => {
			expect(removeNulls({ a: 0, b: false, c: "", d: null })).toEqual({
				a: 0,
				b: false,
				c: "",
			});
		});

		it("returns an empty object if all values are null or undefined", () => {
			expect(removeNulls({ a: null, b: undefined })).toEqual({});
		});

		it("returns the same object if no values are null or undefined", () => {
			expect(removeNulls({ a: 1, b: 2 })).toEqual({ a: 1, b: 2 });
		});
	});

	describe("convertDuration", () => {
		it("should format duration with hours and minutes when duration >= 1 hour", () => {
			expect(convertDuration(3661)).toBe("1 uur, 1 min");
			expect(convertDuration(3600)).toBe("1 uur, 0 min");
			expect(convertDuration(7200)).toBe("2 uur, 0 min");
			expect(convertDuration(7320)).toBe("2 uur, 2 min");
		});

		it("should format duration with only minutes when duration < 1 hour", () => {
			expect(convertDuration(0)).toBe("0 min");
			expect(convertDuration(59)).toBe("0 min");
			expect(convertDuration(60)).toBe("1 min");
			expect(convertDuration(120)).toBe("2 min");
			expect(convertDuration(3599)).toBe("59 min");
		});

		it("should handle large durations correctly", () => {
			expect(convertDuration(36000)).toBe("10 uur, 0 min");
			expect(convertDuration(90061)).toBe("25 uur, 1 min");
		});

		it("should use custom hour and minute labels", () => {
			expect(convertDuration(3661, " hours,", " minutes")).toBe(
				"1 hours, 1 minutes",
			);
			expect(convertDuration(7320, "h", "m")).toBe("2h 2m");
		});

		it("should use custom labels for minutes-only format", () => {
			expect(convertDuration(120, " hours,", " minutes")).toBe("2 minutes");
			expect(convertDuration(59, "h", "m")).toBe("0m");
			expect(convertDuration(1800, " hr ", " mins")).toBe("30 mins");
		});

		it("should handle boundary conditions", () => {
			expect(convertDuration(3599)).toBe("59 min");
			expect(convertDuration(3601)).toBe("1 uur, 0 min");
		});
	});

	describe("convertDistance", () => {
		it("should render full text (meter, kilometer) correctly", () => {
			expect(convertDistance(0, true)).toBe("");
			expect(convertDistance(100, true)).toBe("100 meter");
			expect(convertDistance(1000, true)).toBe("1 kilometer");
			expect(convertDistance(100000, true)).toBe("100 kilometer");
		});

		it("should render abbreviation (m, km) correctly", () => {
			expect(convertDistance(0, false)).toBe("");
			expect(convertDistance(100, false)).toBe("100 m");
			expect(convertDistance(1000, false)).toBe("1 km");
			expect(convertDistance(100000, false)).toBe("100 km");
		});

		it("should render format localString 10.000", () => {
			expect(convertDistance(10000000, false)).toBe("10.000 km");
		});
	});
});

describe("capitalize", () => {
	it("should capitalize the first letter of a string", () => {
		expect(capitalize("hello")).toBe("Hello");
	});

	it("should capitalize the first letter of a string", () => {
		expect(capitalize("hello world")).toBe("Hello world");
	});

	it("should convert rest of string to lowercase", () => {
		expect(capitalize("hELLO")).toBe("Hello");
	});

	it("should handle single character strings", () => {
		expect(capitalize("a")).toBe("A");
	});

	it("should handle empty strings", () => {
		expect(capitalize("")).toBe("");
	});
});

describe("convertDateTime", () => {
	beforeEach(() => {
		// September 19, 1988 15:37:00
		const fixedDate = new Date(1988, 8, 19, 15, 37);
		vi.useFakeTimers();
		vi.setSystemTime(fixedDate);
	});

	afterEach(() => {
		// Restore the real timers
		vi.useRealTimers();
	});

	describe("formats dates correctly", () => {
		it("formats same day", () => {
			expect(convertDateTime("1988-09-19T13:37:00.000Z")).toBe("15:37");
		});
		it("formats tomorrow", () => {
			expect(convertDateTime("1988-09-20T13:37:00.000Z")).toBe(
				"morgen om 15:37",
			);
		});

		it("formats date beyond a week", () => {
			expect(convertDateTime("1988-09-30T13:37:00.000Z")).toBe(
				"vr 30 sep. 14:37",
			);
		});
		it("formats day before", () => {
			expect(convertDateTime("1988-09-18T13:37:00.000Z")).toBe(
				"gisteren om 15:37",
			);
		});
		it("formats week before", () => {
			expect(convertDateTime("1988-09-10T13:37:00.000Z")).toBe(
				"za 10 sep. 15:37",
			);
		});
	});

	describe("convertSecondsToMinutes", () => {
		it("should convert seconds to minutes", () => {
			expect(convertSecondsToMinutes(0)).toBe(0);
			expect(convertSecondsToMinutes(60)).toBe(1);
			expect(convertSecondsToMinutes(3600)).toBe(60);
			expect(convertSecondsToMinutes(3661)).toBe(61);
		});
	});

	describe("convertTimeAriaLabel", () => {
		it("should convert time to screen reader format", () => {
			expect(convertTimeAriaLabel("15:37")).toBe("15 uur 37 minuten");
			expect(convertTimeAriaLabel("05:43")).toBe("05 uur 43 minuten");
			expect(convertTimeAriaLabel("morgen om 05:43")).toBe(
				"morgen om 05 uur 43 minuten",
			);
		});
	});
});
