import {
	STATISTICS_HEADER,
	STATISTICS_VALUE_ROUTEPLANNER,
} from "@anwb/platform-support";

import type { Input } from "../../components/routing/sidepanel/input/types/input.schema";
import type { ApiErrorResponse } from "../api-response";
import type { EvRouteResponse } from "../routing-ev/routing-ev.types";
import type { GetRouteSuccessResponse } from "./routing-api.types";

import {
	genericErrorMessage,
	genericErrorTitle,
} from "../../components/routing/sidepanel/input/error-notification/ErrorNotification.constant";
import { env } from "../../env";
import { isApiErrorResponse, isApiResponse } from "../api-response";
import { getEvRoute } from "../routing-ev/routing-ev.api";
import { transportMode } from "../transport-mode";
import { getRoutingApiUrl } from "./routing-input-api.utils";

/**
 * Calculate the route based on the url state.
 *
 * - For EV we fetch our data from ChargeTrip
 * - For Car/Caravan we fetch our data from TomTom
 * - For Bike/Pedestrian we get the data from Andes
 */
export const getRoute = async (
	input: Input,
): Promise<ApiErrorResponse | EvRouteResponse | GetRouteSuccessResponse> => {
	// For EV we perform a specific POST request.
	if (input.transportMode === transportMode.EV) {
		return getEvRoute(input);
	}

	const routingApiUrl = `${env.NEXT_PUBLIC_APIGEE_URL}/routing/route/v1/route/${input.transportMode}`;

	const url: URL = getRoutingApiUrl(routingApiUrl, input);

	const response = await fetch(url, {
		headers: {
			[STATISTICS_HEADER]: STATISTICS_VALUE_ROUTEPLANNER,
		},
	});

	const result = (await response.json()) as
		| ApiErrorResponse
		| GetRouteSuccessResponse;

	if (isApiErrorResponse(result)) {
		throw new RouteCalculationError(
			result.error.code,
			result.error.title,
			result.error.message,
		);
	}

	if (!isApiResponse(result) && !isApiErrorResponse(result)) {
		throw Error("An unexpected error occurred");
	}

	return result;
};

export class RouteCalculationError extends Error {
	override name = "RouteCalculationError";
	constructor(
		public code = "SERVER_ERROR",
		public title: string = genericErrorTitle,
		public override message: string = genericErrorMessage,
	) {
		super(message);
	}
}
