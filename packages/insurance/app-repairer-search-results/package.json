{"name": "@anwb/insurance-app-repairer-search-results", "description": "The insurance app for the repairers search results page", "version": "1.0.6", "private": true, "type": "module", "main": "dist/index.js", "exports": {".": "./dist/index.js"}, "scripts": {"build": "tsc --project tsconfig.build.json", "dev": "tsc --project tsconfig.build.json --watch", "lint": "eslint --cache --report-unused-disable-directives .", "typecheck": "tsc --noEmit"}, "dependencies": {"@anwb/insurance-service-support": "workspace:*", "@anwb/poncho": "4.64.3", "@tanstack/react-query": "^4.40.0", "react-hook-form": "^7.56.3", "zustand": "^4.5.6"}, "devDependencies": {"@anwb/tools-eslint-config": "workspace:*", "@total-typescript/tsconfig": "^1.0.4", "@types/mapbox-gl": "^3.4.1", "@types/node": "^22.15.17", "@types/react": "^17.0.83", "@types/react-dom": "^17.0.26", "@types/styled-components": "^5.1.34", "eslint": "^9.24.0", "next": "^12.3.7", "react": "^17.0.2", "react-dom": "^17.0.2", "styled-components": "^5.3.11", "typescript": "^5.8.3"}, "peerDependencies": {"react": "^17.0.2", "react-dom": "^17.0.2", "styled-components": "^5.3.11"}}