import { BrandedBanner } from "@anwb/poncho/components/branded-banner/BrandedBanner";
import { ArrowRightIcon } from "@anwb/poncho/components/icon/variants/ui/ArrowRightIcon";
import { Picture } from "@anwb/poncho/components/picture";

import type {
	ArrowOnButton,
	ContentType,
	VariantType,
} from "../../../components/BrandedPanelBanner/types";

import { renderBloomreachDocument } from "../../../../utils/renderBloomreachDocument";
import {
	type MinimalDocumentData as DocumentData,
	MinimalDocumentDataSchema as DocumentDataSchema,
} from "../schemas";
import { mapImageToProps } from "./helpers/mapImageToProps";

type Props = DocumentData & {
	arrowOnButton: ArrowOnButton;
	context: ContentType;
	variant: VariantType;
};

export function BrandedPanelBanner({
	arrowOnButton,
	context,
	href,
	image,
	linkText,
	title,
	variant,
}: Props) {
	const derivedImage = image ? mapImageToProps(image) : null;

	return (
		<BrandedBanner context={context} href={href || ""} variant={variant}>
			<BrandedBanner.Content>
				<BrandedBanner.Title>{title}</BrandedBanner.Title>
				{linkText && href && (
					<BrandedBanner.Button color="primary" variant={context}>
						{linkText} {arrowOnButton && <ArrowRightIcon />}
					</BrandedBanner.Button>
				)}
			</BrandedBanner.Content>
			{derivedImage && (
				<BrandedBanner.Visual {...derivedImage} variant="image">
					<Picture></Picture>
				</BrandedBanner.Visual>
			)}
		</BrandedBanner>
	);
}

export const BrBrandedPanelBanner = renderBloomreachDocument(
	BrandedPanelBanner,
	DocumentDataSchema,
);
