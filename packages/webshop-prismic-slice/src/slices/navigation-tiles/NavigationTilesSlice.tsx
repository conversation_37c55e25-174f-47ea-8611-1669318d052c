import type {
	ContentPageDocumentDataBodyNavigationTilesSliceItem as NavigationTilesItem,
	ContentPageDocumentDataBodyNavigationTilesSlicePrimary as NavigationTilesSlicePrimary,
} from "@anwb/webshop-prismic";

import NavigationTiles from "@anwb/webshop-component-navigation-tiles";

import { PrismicSliceContainer } from "../../styles/prismic-slice.styled";

function NavigationTilesSlice({
	items,
	primary,
}: {
	items: Array<NavigationTilesItem>;
	primary: NavigationTilesSlicePrimary;
}) {
	const { hidden } = primary;

	if (!items.length) return null;

	const styles = hidden ? { height: 0, overflow: "hidden" } : {};

	return (
		<div data-test="navigation-tiles" style={styles}>
			<PrismicSliceContainer>
				<NavigationTiles items={items} primary={primary} />
			</PrismicSliceContainer>
		</div>
	);
}

export default NavigationTilesSlice;
